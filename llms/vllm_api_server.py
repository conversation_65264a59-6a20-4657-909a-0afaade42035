"""
单张视频帧识别服务
用于处理单个图像帧的视觉语言模型(VLM)API服务
单图的是滤池的识别
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import json
import logging
import cv2
import base64
import numpy as np
from llms.config import SYSTEM
from llms.config_filter import SYSTEM_FILTER
from llms.config_aerobic import SYSTEM_AEROBIC
from llms.config_bucket_dipper import SYSTEM_BUCKET_DIPPER
from llms.config_bucket_dipper_shaft import SYSTEM_BUCKET_DIPPER_SHAFT
from llms.config_moss import SYSTEM_MOSS
from llms.config_slag_outlet import SYSTEM_SLAG_OUTLET
from llms.config_secondary_clarifier import SYSTEM_SECONDARY_CLARIFIER
from openai import OpenAI
import re
from config_file import config
from typing import Union

logger = logging.getLogger(__name__)

def frame_numpy_to_base64(frame: np.ndarray) -> str:
    """
    将numpy数组格式的图像转换为base64编码字符串
    
    Args:
        frame (np.ndarray): 输入的图像数组
        
    Returns:
        str: base64编码的图像字符串
    """
    _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
    base64_image = base64.b64encode(buffer).decode('utf-8')
    # 添加data URI前缀，确保符合API要求
    return f"data:image/jpeg;base64,{base64_image}"

def frame_img_to_base64(img_path: str) -> str:
    """
    将图片文件转换为base64编码字符串
    
    Args:
        img_path (str): 图片文件的路径
        
    Returns:
        str: base64编码的图像字符串
    """
    with open(img_path, 'rb') as img_file:
        img_base = base64.b64encode(img_file.read()).decode('utf-8')
    # 根据文件扩展名确定MIME类型
    ext = os.path.splitext(img_path)[1].lower()
    mime_type = "image/jpeg"  # 默认MIME类型
    if ext == ".png":
        mime_type = "image/png"
    elif ext == ".gif":
        mime_type = "image/gif"
    elif ext == ".webp":
        mime_type = "image/webp"
    # 添加data URI前缀，确保符合API要求
    return f"data:{mime_type};base64,{img_base}"

def process_image(
    frame: Union[np.ndarray, str],
    system_type: str,
    max_retries: int = 3,
    retry_delay: int = 2
) -> dict:
    """
    处理单张图片并通过VLM模型进行分析
    
    Args:
        frame: 输入图像，可以是numpy数组或图片路径字符串
        system_type (str): 系统提示词类型，可选值：'system_prompt1' 或 'system_prompt2'
        max_retries (int): API调用最大重试次数，默认3次
        retry_delay (int): 重试间隔时间(秒)，默认2秒
    
    Returns:
        dict: 模型分析结果的字典
            成功: 包含分析结果的字典
            失败: 空字典 {}
    """
    # 将输入图像转换为base64格式
    if isinstance(frame, np.ndarray):
        base64_image = frame_numpy_to_base64(frame)
    else:
        base64_image = frame_img_to_base64(frame)
   
    # 使用配置实例中的环境变量初始化OpenAI客户端
    client = OpenAI(
        api_key=config.env_vars.get("QWEN2_VL_API_KEY"),
        base_url=config.env_vars.get("QWEN2_VL_BASE_URL")
    )
    model = config.env_vars.get("QWEN2_VL_MODEL")

    # 选择系统提示词
    query = "根据提示词分析图片并输出内容。"
    print(f"---------------------------")
    print(f"图片系统提示词是：: {system_type}")
    print(f"---------------------------")
    if system_type == 'system_prompt_filter1':
        system_prompt = SYSTEM_FILTER['system_prompt_filter1']
    elif system_type == 'system_prompt_aerobic_single1':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_single1']
    elif system_type == 'system_prompt_aerobic_single2':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_single2']
    elif system_type == 'system_prompt_aerobic_single1_air_bubble':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_single1_air_bubble']
    elif system_type == 'system_prompt_aerobic_single2_air_bubble':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_single2_air_bubble']
    elif system_type == 'system_prompt_aerobic_single1_bubble_area':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_single1_bubble_area']
    elif system_type == 'system_prompt_aerobic_single2_bubble_area':
        system_prompt = SYSTEM_AEROBIC['system_prompt_aerobic_single2_bubble_area']
    elif system_type == 'system_prompt_bucket_dipper':
        system_prompt = SYSTEM_BUCKET_DIPPER['system_prompt_bucket_dipper']
    elif system_type == 'system_prompt_waste_percentage':
        system_prompt = SYSTEM_BUCKET_DIPPER_SHAFT['system_prompt_waste_percentage']
    elif system_type == 'system_prompt_bulky_waste':
        system_prompt = SYSTEM_BUCKET_DIPPER_SHAFT['system_prompt_bulky_waste']
    elif system_type == 'system_prompt_slat_damage':
        system_prompt = SYSTEM_BUCKET_DIPPER_SHAFT['system_prompt_slat_damage']
    elif system_type == 'system_prompt_moss':
        system_prompt = SYSTEM_MOSS['system_prompt_moss']
    elif system_type == 'system_prompt_slag_outlet':
        system_prompt = SYSTEM_SLAG_OUTLET['system_prompt_slag_outlet']
    # 整体视角分析
    elif system_type == 'system_prompt_holistic_perspective':
        system_prompt = SYSTEM_SECONDARY_CLARIFIER['system_prompt_holistic_perspective']
    # 树叶识别
    elif system_type == 'system_prompt_leaf_recognition':
        system_prompt = SYSTEM_SECONDARY_CLARIFIER['system_prompt_leaf_recognition']
    # 排浮渣识别
    elif system_type == 'system_prompt_slag_outletv2':
        system_prompt = SYSTEM_SECONDARY_CLARIFIER['system_prompt_slag_outletv2']
    # 排渣堰门浮渣污泥监控分析
    elif system_type == 'system_prompt_slag_weir_gate':
        system_prompt = SYSTEM_SECONDARY_CLARIFIER['system_prompt_slag_weir_gate']
    else:
        # 默认使用system_prompt2作为备用提示词
        system_prompt = SYSTEM['system_prompt2']
        logging.warning(f"未知的system_type: {system_type}，使用默认提示词")
        
    # 构建API请求消息
    messages = [
        {'role': 'system', 'content': system_prompt },
        {
            'role': 'user',
            'content': [
                {'type': 'image_url', 'image_url': {'url': base64_image}},
                {'type': 'text', 'text': query},
            ]
        }
    ]
    
    # 重试逻辑
    for attempt in range(max_retries):
        try:
            # 调用API
            resp = client.chat.completions.create(
                model=model,
                messages=messages,
                seed=42,
                temperature=0.2
            )
            response_text = resp.choices[0].message.content
            # print(response_text)
            logger.info(f"原始响应: {response_text}")
            # 尝试解析JSON响应
            try:
                # 提取JSON部分
                json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    response_dict = json.loads(json_str)
                else:
                    # 如果没有JSON代码块标记，尝试直接解析整个响应
                    response_dict = json.loads(response_text)
                
                return response_dict
            except json.JSONDecodeError:
                # 如果JSON解析失败，使用正则表达式作为备选方案
                pattern = r'"(.*?)"\s*[:：]\s*(?:"(.*?)"|(\[.*?\])|([^",\s]+))'
                matches = re.findall(pattern, response_text)
                # 合并捕获组获得值
                response_dict = {key: (value1 or value2 or value3) for key, value1, value2, value3 in matches}
                
                if response_dict:
                    return response_dict
            
        except Exception as e:
            logger.error(f"API 调用或解析错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
    
    logger.error("所有重试都失败了")
    return {}

if __name__ == "__main__":
    
    # system_prompt_filter1/ystem_prompt_aerobic_single1
    # process_image('assets/frame_4010_2024_12_06_17_48_30.jpg', system_type='system_prompt_filter1') # 滤池
    # process_image('assets/frame_1.jpg', system_type='system_prompt_filter1') # 滤池
    # process_image('assets/frame.jpg', system_type='system_prompt_aerobic_single1') # 好氧池
    
    # process_image('assets/bucket_dipper_tilt.jpg', system_type='system_prompt_bucket_dipper') # 耙斗
    # process_image('assets/bucket_dipper_shaft.jpg', system_type='system_prompt_slat_damage') # 栅条损坏检测
    
    # process_image('assets/moss_picture.jpg', system_type='system_prompt_moss') # 青苔检测
    '''
        ```json
    {
    "是否为青苔": "是",
    "图片分析结果": "青苔生长状况较为严重，主要分布在排水孔下方的垂直区域，表面有湿润的、绒毛状的质地，颜色特征为鲜艳的绿色到深绿色不等，生长模式呈现垂直流淌状的生长痕迹。",
    "处理建议": "建议定期清理排水孔下方的青苔，保持排水孔的畅通，避免积水，同时加强排水系统的维护，减少青苔的生长环境。"
    }
    ```
    '''
    # process_image('assets/slag_outlet_picture.png', system_type='system_prompt_slag_outlet') # 泡沫检测
    # process_image('assets/california-drought-la-wastewater-recycling-climate-file.jpg', system_type='system_prompt_holistic_perspective') # 二沉池整体视角分析
    # process_image('assets/leaf_recognition_test.jpg', system_type='system_prompt_leaf_recognition') # 树叶识别
    # process_image('assets/slag_outletv2_test.png', system_type='system_prompt_slag_outletv2') # 排浮渣识别
# 排渣堰门浮渣污泥监控分析
    process_image('assets/slag_weir_gate_image.png', system_type='system_prompt_slag_weir_gate') 